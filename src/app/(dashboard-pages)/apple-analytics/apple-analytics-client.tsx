"use client";

import React from "react";
import {
  B<PERSON><PERSON><PERSON>b,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Import Apple Analytics components
import { AppleAnalyticsMetrics } from "@/components/dashboard/apple-analytics-metrics"
import { AppleAnalyticsCharts } from "@/components/dashboard/apple-analytics-charts"

// Import the hook for Apple Analytics data
import { useAppleAnalytics } from "@/hooks/useAppleAnalytics";

export default function AppleAnalyticsPageClient() {
  // State for time range
  const [timeRange, setTimeRange] = React.useState("all");

  // Fetch Apple Analytics data
  const {
    data: appleData,
    loading: appleLoading,
    error: appleError,
    refetch: refetchApple
  } = useAppleAnalytics({ timeRange });

  // Handle time range changes
  const handleTimeRangeChange = (newTimeRange: string) => {
    setTimeRange(newTimeRange);
  };

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/home">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Apple Analytics</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Apple Analytics</h1>
        <p className="text-muted-foreground">
          Monitor app store performance, installation trends, and user acquisition metrics.
        </p>
      </div>

      {/* Apple Analytics Charts */}
      <AppleAnalyticsCharts
        analyticsData={appleData}
        loading={appleLoading}
        error={appleError}
      />
    </div>
  );
}
