"use client"; // Make it a client component to use hooks

import { usePathname } from "next/navigation"; // Import usePathname
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useMetrics } from "@/contexts/metrics-context";

interface SiteHeaderProps {
  className?: string;
}

// Helper function to get title from pathname
function getTitleFromPathname(pathname: string): string {
  const segments = pathname.split("/").filter(Boolean);
  const lastSegment = segments[segments.length - 1] || "home";

  switch (lastSegment) {
    case "home":
      return "Dashboard";
    case "metrics":
      return "Metrics";
    case "mobile":
      return "Mobile";
    case "engage":
      return "Engage";
    case "analytics":
      return "Analytics";
    case "history":
      return "History";
    case "profile":
      return "Profile Setup";
    case "settings":
      return "Settings";
    case "help":
      return "Help & Resources";
    default:
      return "Dashboard"; // Fallback title
  }
}

// Helper function to get password for specific routes
function getPasswordForRoute(pathname: string): string | null {
  const segments = pathname.split("/").filter(Boolean);
  const lastSegment = segments[segments.length - 1] || "home";

  switch (lastSegment) {
    case "affiliates":
      return "gFYA<8ZsPK";
    default:
      return null;
  }
}

export function SiteHeader({ className }: SiteHeaderProps) {
  const pathname = usePathname();
  const title = getTitleFromPathname(pathname);
  
  // Get password from route or metrics context
  let password = getPasswordForRoute(pathname);
  
  // For metrics page, use the context to get dynamic password
  let metricsContext = null;
  try {
    metricsContext = useMetrics();
  } catch {
    // Not in metrics context, ignore
  }
  
  if (pathname.includes('/metrics') && metricsContext) {
    password = metricsContext.getPasswordForActiveTab();
  }

  return (
    <header
      className={cn(
        "group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 transition-[width,height] ease-linear",
        className
      )}
    >
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        {password && (
          <div className="ml-auto text-sm text-muted-foreground">
            password: {password}
          </div>
        )}
      </div>
    </header>
  );
}
