import { redirect } from "next/navigation";

/**
 * Redirects to a specified path with an encoded message as a query parameter.
 * @param {('error' | 'success')} type - The type of message, either 'error' or 'success'.
 * @param {string} path - The path to redirect to.
 * @param {string} message - The message to be encoded and added as a query parameter.
 * @returns {never} This function doesn't return as it triggers a redirect.
 */
export function encodedRedirect(
  type: "error" | "success",
  path: string,
  message: string
) {
  return redirect(`${path}?${type}=${encodeURIComponent(message)}`);
}

/**
 * Validates a URL string for public, http(s) URLs only (no localhost/private IPs).
 * @param urlString The URL to validate
 * @returns { isValid: boolean, error?: string }
 */
export function validateUrl(urlString: string): {
  isValid: boolean;
  error?: string;
} {
  if (!urlString || !urlString.trim()) {
    return { isValid: false, error: "URL is required" };
  }
  try {
    if (!urlString.match(/^https?:\/\//)) {
      return {
        isValid: false,
        error: "URL must start with http:// or https://",
      };
    }
    const url = new URL(urlString);
    if (!url.hostname || url.hostname.length < 3) {
      return { isValid: false, error: "Invalid domain name" };
    }
    if (!url.hostname.includes(".")) {
      return { isValid: false, error: "Invalid domain format" };
    }
    if (
      url.hostname === "localhost" ||
      url.hostname.startsWith("127.") ||
      url.hostname.startsWith("192.168.") ||
      url.hostname.startsWith("10.") ||
      url.hostname.endsWith(".local")
    ) {
      return {
        isValid: false,
        error: "Local or private URLs are not supported",
      };
    }
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: "Invalid URL format" };
  }
}
